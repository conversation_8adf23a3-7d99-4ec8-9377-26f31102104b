/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (blade<PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.miniapp.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springblade.business.user.service.IRechargeService;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.WebUtil;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * 充值控制器
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
@RestController
@AllArgsConstructor
@RequestMapping("/blade-chat/user/recharge")
@Tag(name = "用户充值", description = "用户钱包充值相关接口")
public class WeChatRechargeController extends BladeController {

    private final IRechargeService rechargeService;

    /**
     * 获取充值配置
     */
    @GetMapping("/config")
    @ApiOperationSupport(order = 1)
    @Operation(summary = "获取充值配置", description = "获取充值金额限制和建议金额配置")
    public R<Map<String, Object>> getRechargeConfig() {
        Map<String, Object> config = rechargeService.getRechargeAmountConfig();
        return R.data(config);
    }

    /**
     * 创建充值订单
     */
    @PostMapping("/order/create")
    @ApiOperationSupport(order = 2)
    @Operation(summary = "创建充值订单", description = "创建充值订单并返回支付参数")
    public R<Map<String, Object>> createRechargeOrder(
            @Parameter(description = "充值金额") @RequestParam BigDecimal amount,
            @Parameter(description = "用户openId") @RequestParam String openId,
            @Parameter(description = "支付配置名称", required = false) @RequestParam(required = false) String configName) {

        BladeUser user = AuthUtil.getUser();
        if (user == null) {
            return R.fail("用户未登录");
        }

//        if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
//            return R.fail("充值金额无效");
//        }

        if (openId == null || openId.trim().isEmpty()) {
            return R.fail("用户openId不能为空");
        }

        try {
            // 获取客户端IP
            String clientIp = WebUtil.getIP(getRequest());

            Map<String, Object> result = rechargeService.createRechargeOrder(
                user.getUserId(),
                amount,
                openId,
                clientIp,
                configName
            );

            return R.data(result);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 查询充值订单状态
     */
    @GetMapping("/order/status")
    @ApiOperationSupport(order = 3)
    @Operation(summary = "查询订单状态", description = "根据订单号查询充值订单状态")
    public R<Map<String, Object>> queryOrderStatus(@Parameter(description = "订单号") @RequestParam String orderNo) {
        if (orderNo == null || orderNo.trim().isEmpty()) {
            return R.fail("订单号不能为空");
        }

        Map<String, Object> result = rechargeService.queryRechargeOrderStatus(orderNo);
        if (result == null) {
            return R.fail("订单不存在");
        }

        return R.data(result);
    }

    /**
     * 取消充值订单
     */
    @PostMapping("/order/cancel")
    @ApiOperationSupport(order = 4)
    @Operation(summary = "取消充值订单", description = "取消待支付的充值订单")
    public R<Boolean> cancelOrder(
            @Parameter(description = "订单号") @RequestParam String orderNo,
            @Parameter(description = "取消原因", required = false) @RequestParam(required = false) String reason) {

        if (orderNo == null || orderNo.trim().isEmpty()) {
            return R.fail("订单号不能为空");
        }

        boolean result = rechargeService.cancelRechargeOrder(orderNo, reason != null ? reason : "用户主动取消");
        return R.status(result);
    }

    /**
     * 获取用户充值记录
     */
    @GetMapping("/records")
    @ApiOperationSupport(order = 5)
    @Operation(summary = "获取充值记录", description = "分页获取当前用户的充值记录")
    public R<Map<String, Object>> getRechargeRecords(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer current,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") Integer size) {

        BladeUser user = AuthUtil.getUser();
        if (user == null) {
            return R.fail("用户未登录");
        }

        Map<String, Object> result = rechargeService.getUserRechargeRecords(user.getUserId(), current, size);
        return R.data(result);
    }

    /**
     * 管理员获取用户充值记录
     */
    @GetMapping("/records/{userId}")
    @ApiOperationSupport(order = 6)
    @Operation(summary = "获取指定用户充值记录", description = "管理员分页获取指定用户的充值记录")
    public R<Map<String, Object>> getUserRechargeRecords(
            @Parameter(description = "用户ID") @PathVariable Long userId,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer current,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") Integer size) {

        if (userId == null) {
            return R.fail("用户ID不能为空");
        }

        Map<String, Object> result = rechargeService.getUserRechargeRecords(userId, current, size);
        return R.data(result);
    }

    /**
     * 测试支付配置
     */
    @GetMapping("/test/config")
    @ApiOperationSupport(order = 7)
    @Operation(summary = "测试支付配置", description = "检查微信支付配置是否正常")
    public R<Map<String, Object>> testPaymentConfig() {
        try {
            Map<String, Object> result = new HashMap<>();

            // 检查充值配置
            Map<String, Object> rechargeConfig = rechargeService.getRechargeAmountConfig();
            result.put("rechargeConfig", rechargeConfig);

            // 检查微信支付配置（这里需要添加检查方法）
            result.put("wechatPayConfigExists", "需要检查微信支付配置");

            return R.data(result);
        } catch (Exception e) {
            return R.fail("检查配置失败: " + e.getMessage());
        }
    }


}
